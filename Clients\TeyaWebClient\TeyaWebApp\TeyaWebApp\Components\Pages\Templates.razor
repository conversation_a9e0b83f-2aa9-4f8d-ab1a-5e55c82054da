﻿@page "/templates"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaUIViewModels.ViewModels
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "templatesAccessPolicy")]
@using MudBlazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns
@using TeyaWebApp.Components.Layout
@using Syncfusion.Blazor.Grids
@using System.Text.Json
@using TeyaWebApp.TeyaAIScribeResources
@inject ITemplateService TemplateService
@inject IVisitTypeService VisitTypeService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ILogger<Templates> Logger
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject IMemberService MemberService
@using Syncfusion.Blazor.Navigations
@layout Admin
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Style="padding: 24px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
    <MudGrid>
        <MudItem xs="12">
            <MudPaper Class="pa-4" Elevation="0">
                <MudText Typo="Typo.h6" Class="mb-4 ml-6">Available Templates</MudText>
                <MudItem xs="12">
                    <div>
                        <SfDropDownList TValue="string" TItem="string"
                                        Placeholder="Select Visit Type"
                                        DataSource="@data"
                                        @bind-Value="@DropDownValue"
                                        Width="300px">
                            <DropDownListEvents TValue="string" TItem="string" OnValueSelect="@OnValueSelecthandler" />
                        </SfDropDownList>

                        <SfButton OnClick="ResetSelection"
                                  CssClass="e-primary"
                                  style="height: 30px; padding: 0 16px;">
                            Reset
                        </SfButton>
                    </div>
                </MudItem>
                <div style="max-width: 1200px;">
                    <GenericGrid TValue="TemplateData" DataSource="@ProviderData" GridLines="GridLine.Both">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel" />
                        <GridPageSettings PageSize="10" />
                        <GridColumns>
                            <GridColumn Field="@nameof(TemplateData.IsDefault)" HeaderText="@Localizer["Default"]" Width="150" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                    }
                                    <SfCheckBox Checked="@templateData?.IsDefault" @onchange="@(args => OnIsDefaultChange(args, templateData))" />
                                </Template>
                            </GridColumn>
                            <GridColumn Field="@nameof(TemplateData.VisitType)" HeaderText="@Localizer["Visit Type"]" Width="200" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                    }
                                    <span>@(string.IsNullOrEmpty(templateData?.VisitType) ? @Localizer["Not Assigned"] : templateData.VisitType)</span>
                                </Template>
                            </GridColumn>
                            <GridColumn Field="@nameof(TemplateData.TemplateName)" HeaderText="@Localizer["Template Name"]" Width="200" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                    }
                                    <span style="cursor: pointer;" @onclick="() => EditTemplate(templateData)">
                                        @templateData.TemplateName
                                    </span>
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Sections" Width="500" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                        var templateDictionary = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, TemplateField>>>(templateData.Template);
                                        List<string> Keys = new List<string>();
                                        if (templateDictionary != null)
                                        {
                                            Keys = templateDictionary.Values
                                            .Where(innerDict => innerDict != null)
                                            .SelectMany(innerDict => innerDict.Keys)
                                            .ToList();
                                        }
                                        string allKeys = string.Join(", ", Keys);
                                        int maxLength = 50;
                                        string displayText = allKeys.Length > maxLength ? allKeys.Substring(0, maxLength) + "..." : allKeys;
                                    }

                                    <span style="cursor: pointer;" @onclick="() => EditTemplate(templateData)">
                                        @displayText
                                    </span>
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Actions" Width="140" TextAlign="TextAlign.Center">
                                <Template Context="data">
                                    @{
                                        var templateData = data as TemplateData;
                                    }
                                    <div style="display: inline-flex; gap: 8px;">
                                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" OnClick="@(() => EditTemplate(templateData))" />
                                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" OnClick="@(() => DeleteTemplate(templateData))" />
                                    </div>
                                </Template>
                            </GridColumn>

                        </GridColumns>
                    </GenericGrid>
                </div>


                <MudGrid>
                    <MudItem xs="12" Class="mt-6 ml-6">
                        <GenericButton Variant="Variant.Filled" OnClick="@ToggleCardVisibility" ButtonStyle="margin-top: 20px; margin-bottom: 20px; display: block; width: 200px;">
                            @Localizer["+ New Template"]
                        </GenericButton>
                    </MudItem>

                    @if (isAddSectionVisible)
                    {
                        <MudGrid GutterSize="16px" Class="mt-4 ml-2 mr-2">
                            <MudItem xs="12" sm="2" md="2">
                                <MudText Typo="Typo.h6" Class="mb-2">Sections</MudText>
                                <MudList T="TemplateSection" Class="medical-sections">
                                    @foreach (var sectionDisplay in TemplateSections)
                                    {
                                        <MudListItem Class="custom-disabled-listheader-item" Disabled="true"><b>@sectionDisplay.SectionName</b></MudListItem>
                                        @foreach (var field in sectionDisplay.Fields)
                                        {
                                            <MudListItem Class="custom-disabled-list-item" Disabled="true" Value="field" Style="color:black;">@field.FieldName</MudListItem>
                                        }
                                    }
                                </MudList>
                            </MudItem>

                            <MudItem xs="12" sm="6" md="6" Style="max-width: 1000px;">
                                <MudText Typo="Typo.h6" Class="mb-2">Section Settings</MudText>
                                <MudCard Style="width: 100%;">
                                    <MudCardContent>
                                        <MudGrid Style="padding-bottom: 10px;">
                                            <MudItem xs="12">
                                                <div class="template-name-visit-type-row">
                                                    <div class="template-name-field">
                                                        <MudTextField Label="@Localizer["Template Name"]"
                                                                      @bind-Value="@templateName"
                                                                      Required="true"
                                                                      Variant="Variant.Outlined">
                                                        </MudTextField>
                                                    </div>
                                                    <div class="visit-type-container">
                                                        <MudSelect T="string"
                                                                   Label="Select Visit Type"
                                                                   @bind-Value="@SelectedVisitType"
                                                                   Variant="Variant.Outlined"
                                                                   Dense="true">
                                                            @foreach (var visitType in VisitTypes)
                                                            {
                                                                <MudSelectItem Value="@visitType.VisitName">@visitType.VisitName</MudSelectItem>
                                                            }
                                                        </MudSelect>
                                                    </div>
                                                </div>
                                            </MudItem>
                                        </MudGrid>
                                        <div class="headers-container">
                                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                                <MudText Typo="Typo.h6">Template Headers</MudText>
                                                <MudButton Size="Size.Small" OnClick="@AddSectionHeader" Color="Color.Primary" Typo="Typo.body2">
                                                    Add Header
                                                </MudButton>
                                            </div>

                                            @for (int j = 0; j < TemplateSections.Count; j++)
                                            {
                                                var sectionIndex = j;
                                                <div class="header-section-card">
                                                    <div class="header-title-row">
                                                        <MudButton OnClick="@(() => ToggleSection(sectionIndex))" Color="Color.Primary" Variant="Variant.Text">
                                                            <MudIcon Icon="@(TemplateSections[sectionIndex].IsExpanded ? Icons.Material.Filled.ExpandLess : Icons.Material.Filled.ExpandMore)" />
                                                            <MudText Style="font-weight:bold; margin-left: 8px;">@TemplateSections[sectionIndex].SectionName</MudText>
                                                        </MudButton>
                                                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                       Color="Color.Error"
                                                                       OnClick="@(() => DeleteSectionConfirmation(sectionIndex))" />
                                                    </div>

                                                    @if (TemplateSections[sectionIndex].IsExpanded)
                                                    {
                                                        <div class="uniform-field-row">
                                                            <MudText Typo="Typo.body2" Class="uniform-field-label">Header Title</MudText>
                                                            <MudTextField @bind-Value="@TemplateSections[sectionIndex].SectionName"
                                                                          Variant="Variant.Outlined"
                                                                          Margin="Margin.Dense"
                                                                          Class="uniform-input-field" />
                                                        </div>

                                                        <div class="add-section-button-row">
                                                            <MudButton Size="Size.Small" Color="Color.Primary" OnClick="@(() => AddTextBox(sectionIndex))" Typo="Typo.body2">
                                                                Add Section
                                                            </MudButton>
                                                        </div>

                                                        @for (int i = 0; i < TemplateSections[sectionIndex].Fields.Count; i++)
                                                        {
                                                            var fieldIndex = i;
                                                            <MudCard Class="section-card">
                                                                <MudCardHeader Class="py-1">
                                                                    <div class="d-flex justify-content-between align-items-center">
                                                                        <MudText Typo="Typo.body1">@TemplateSections[sectionIndex].Fields[fieldIndex].FieldName</MudText>
                                                                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                                       Color="Color.Error"
                                                                                       OnClick="@(() => RemoveTextBox(fieldIndex, sectionIndex))" />
                                                                    </div>
                                                                </MudCardHeader>
                                                                <MudDivider DividerType="DividerType.Middle" />
                                                                <MudCardContent>
                                                                    <div class="uniform-field-row">
                                                                        <MudText Typo="Typo.body2" Class="uniform-field-label">Section Title</MudText>
                                                                        <MudTextField @bind-Value="@TemplateSections[sectionIndex].Fields[fieldIndex].FieldName"
                                                                                      Variant="Variant.Outlined"
                                                                                      Margin="Margin.Dense"
                                                                                      Class="uniform-input-field" />
                                                                    </div>

                                                                    <div class="uniform-field-row">
                                                                        <MudText Typo="Typo.body2" Class="uniform-field-label">Section style</MudText>
                                                                        <div class="uniform-button-group">
                                                                            <MudButtonGroup Color="Color.Primary" Class="compact-button-group">
                                                                                @foreach (var style in new[] { "Auto", "Bullet", "Paragraph" })
                                                                                {
                                                                                    var currentStyle = style;
                                                                                    <MudButton Size="Size.Small"
                                                                                               Color="Color.Primary"
                                                                                               Variant="@GetButtonVariant(currentStyle, fieldIndex, sectionIndex)"
                                                                                               OnClick="@(() => SetSectionStyle(currentStyle, fieldIndex, sectionIndex))"
                                                                                               Class="compact-style-button">@Localizer[currentStyle]</MudButton>
                                                                                }
                                                                            </MudButtonGroup>
                                                                        </div>
                                                                    </div>

                                                                    <div class="uniform-field-row">
                                                                        <MudText Typo="Typo.body2" Class="uniform-field-label">Instructions</MudText>
                                                                        <MudTextField @bind-Value="TemplateSections[sectionIndex].Fields[fieldIndex].Instructions"
                                                                                      Variant="Variant.Outlined"
                                                                                      Margin="Margin.Dense"
                                                                                      Class="uniform-input-field" />
                                                                    </div>
                                                                </MudCardContent>
                                                            </MudCard>
                                                        }
                                                    }
                                                </div>
                                            }
                                        </div>

                                        <div class="submit-button-container">
                                            <MudButton Size="Size.Large"
                                                       Color="Color.Primary"
                                                       OnClick="@SubmitForm"
                                                       Typo="Typo.body1"
                                                       Style="padding: 10px 24px; font-size: 1rem;">
                                                <MudIcon Icon="@Icons.Material.Filled.Send" Class="mr-2" />
                                                @Localizer["ADD"]
                                            </MudButton>
                                        </div>
                                    </MudCardContent>
                                </MudCard>
                            </MudItem>
                        </MudGrid>
                    }
                </MudGrid>
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>

@if (showConfirmDialog)
{
    <div style="position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background-color: rgba(0, 0, 0, 0.4); z-index: 1500; display: flex; justify-content: center; align-items: center;">
        <MudPaper Elevation="24" Class="pa-6" Style="background: white; border-radius: 12px; min-width: 300px; max-width: 90vw;">
            <MudText Typo="Typo.h6" Class="mb-2">@confirmDialogTitle</MudText>
            <MudText Typo="Typo.body1" Class="mb-4">@confirmDialogMessage</MudText>
            <div class="d-flex justify-content-end mt-4">
                <MudButton OnClick="CancelConfirmation" Color="Color.Default" Variant="Variant.Outlined" Class="mr-2">Cancel</MudButton>
                <MudButton OnClick="ConfirmAction" Color="Color.Primary" Variant="Variant.Filled">Confirm</MudButton>
            </div>
        </MudPaper>
    </div>
}
<style>
    /* Original styles */
    .medical-sections .e-list-item {
        padding: 12px 0;
        color: #5f6368;
        font-size: 14px;
        border-bottom: 1px solid #ebedf0;
    }

    .custom-disabled-list-item.mud-list-item-disabled {
        color: silver !important;
        opacity: 1 !important;
        cursor: default;
        pointer-events: none;
    }

    .custom-disabled-listheader-item.mud-list-item-disabled {
        color: grey !important;
        opacity: 1 !important;
        cursor: default;
        pointer-events: none;
    }

    /* FIXED: Template Name and Visit Type alignment - same line and height */
    .template-name-visit-type-row {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 16px;
    }

    /* FIXED: Template Name Field - consistent sizing with proper label positioning */
    .template-name-field {
        flex: 1;
        max-width: 300px;
    }

        .template-name-field .mud-input-control {
            height: 56px !important;
            min-height: 56px !important;
        }

        .template-name-field .mud-input-control-input-container {
            height: 56px !important;
            min-height: 56px !important;
        }

        .template-name-field .mud-input {
            height: 56px !important;
            padding: 20px 12px 6px 12px !important;
            font-size: 14px !important;
            line-height: 20px !important;
            border: 1px solid #c4c4c4 !important;
            border-radius: 4px !important;
            background: white !important;
        }

        /* Label positioning - starts inside field, moves up on focus/value */
        .template-name-field .mud-input-label {
            position: absolute !important;
            top: 50% !important;
            left: 12px !important;
            transform: translateY(-50%) !important;
            font-size: 14px !important;
            color: #666 !important;
            background: white !important;
            padding: 0 4px !important;
            transition: all 0.2s ease !important;
            pointer-events: none !important;
            z-index: 2 !important;
        }

        /* Label moves up when field is focused or has value */
        .template-name-field .mud-input-control:focus-within .mud-input-label,
        .template-name-field .mud-input-control.mud-input-control-filled .mud-input-label,
        .template-name-field .mud-input:not(:placeholder-shown) ~ .mud-input-label,
        .template-name-field .mud-input[value]:not([value=""]) ~ .mud-input-label {
            top: 0 !important;
            left: 8px !important;
            transform: translateY(-50%) !important;
            font-size: 12px !important;
            color: #666 !important;
            background: white !important;
            padding: 0 4px !important;
        }

        /* Keep label up when input has any content */
        .template-name-field .mud-input:valid ~ .mud-input-label,
        .template-name-field .mud-input:not(:empty) ~ .mud-input-label {
            top: 0 !important;
            left: 8px !important;
            transform: translateY(-50%) !important;
            font-size: 12px !important;
            color: #666 !important;
            background: white !important;
            padding: 0 4px !important;
        }

    /* FIXED: Visit Type dropdown - matching height with proper label positioning */
    .visit-type-container {
        flex: 1;
        max-width: 250px;
    }

        .visit-type-container .mud-select {
            height: 56px !important;
            min-height: 56px !important;
        }

        .visit-type-container .mud-input-control {
            height: 56px !important;
            min-height: 56px !important;
        }

        .visit-type-container .mud-input-control-input-container {
            height: 56px !important;
            min-height: 56px !important;
        }

        .visit-type-container .mud-input {
            height: 56px !important;
            padding: 20px 12px 6px 12px !important;
            font-size: 14px !important;
            line-height: 20px !important;
            border: 1px solid #c4c4c4 !important;
            border-radius: 4px !important;
            background: white !important;
        }

        /* Label positioning for dropdown - starts inside field, moves up on focus/value */
        .visit-type-container .mud-input-label {
            position: absolute !important;
            top: 50% !important;
            left: 12px !important;
            transform: translateY(-50%) !important;
            font-size: 14px !important;
            color: #666 !important;
            background: white !important;
            padding: 0 4px !important;
            transition: all 0.2s ease !important;
            pointer-events: none !important;
            z-index: 2 !important;
        }

        /* Label moves up when dropdown is focused or has value */
        .visit-type-container .mud-input-control:focus-within .mud-input-label,
        .visit-type-container .mud-input-control.mud-input-control-filled .mud-input-label,
        .visit-type-container .mud-input:not(:placeholder-shown) ~ .mud-input-label,
        .visit-type-container .mud-input[value]:not([value=""]) ~ .mud-input-label {
            top: 0 !important;
            left: 8px !important;
            transform: translateY(-50%) !important;
            font-size: 12px !important;
            color: #666 !important;
            background: white !important;
            padding: 0 4px !important;
        }

        /* Keep label up when dropdown has any selection */
        .visit-type-container .mud-input:valid ~ .mud-input-label,
        .visit-type-container .mud-input:not(:empty) ~ .mud-input-label,
        .visit-type-container .mud-select .mud-input-label.mud-input-label-filled {
            top: 0 !important;
            left: 8px !important;
            transform: translateY(-50%) !important;
            font-size: 12px !important;
            color: #666 !important;
            background: white !important;
            padding: 0 4px !important;
        }

    /* ADDITIONAL: Force label to stay up when field has content - MudBlazor specific */
    .template-name-field .mud-input-control.mud-input-control-filled .mud-input-label,
    .template-name-field .mud-input-control:not(.mud-input-control-empty) .mud-input-label,
    .template-name-field .mud-input-control[data-filled="true"] .mud-input-label {
        top: 0 !important;
        left: 8px !important;
        transform: translateY(-50%) !important;
        font-size: 12px !important;
        color: #666 !important;
        background: white !important;
        padding: 0 4px !important;
    }

    .visit-type-container .mud-input-control.mud-input-control-filled .mud-input-label,
    .visit-type-container .mud-input-control:not(.mud-input-control-empty) .mud-input-label,
    .visit-type-container .mud-input-control[data-filled="true"] .mud-input-label {
        top: 0 !important;
        left: 8px !important;
        transform: translateY(-50%) !important;
        font-size: 12px !important;
        color: #666 !important;
        background: white !important;
        padding: 0 4px !important;
    }

    /* Force label up when input has any value at all */
    .template-name-field .mud-input[value]:not([value=""]) + .mud-input-label,
    .template-name-field .mud-input:not([value=""]) + .mud-input-label,
    .visit-type-container .mud-input[value]:not([value=""]) + .mud-input-label,
    .visit-type-container .mud-input:not([value=""]) + .mud-input-label {
        top: 0 !important;
        left: 8px !important;
        transform: translateY(-50%) !important;
        font-size: 12px !important;
        color: #666 !important;
        background: white !important;
        padding: 0 4px !important;
    }

    /* COMPLETELY REMOVE ALL BLUE BORDERS AND FOCUS EFFECTS */
    .mud-input,
    .mud-input-control .mud-input,
    .mud-select .mud-input,
    .mud-text-field .mud-input,
    .mud-input-outlined,
    .mud-input-outlined .mud-input,
    .uniform-input-field .mud-input,
    .template-name-field .mud-input,
    .visit-type-container .mud-input {
        border: 1px solid #c4c4c4 !important;
        border-radius: 4px !important;
        box-shadow: none !important;
        outline: none !important;
        background: white !important;
    }

        /* Remove blue border on hover */
        .mud-input:hover,
        .mud-input-control:hover .mud-input,
        .mud-select:hover .mud-input,
        .mud-text-field:hover .mud-input,
        .mud-input-outlined:hover,
        .mud-input-outlined:hover .mud-input,
        .uniform-input-field:hover .mud-input,
        .template-name-field:hover .mud-input,
        .visit-type-container:hover .mud-input {
            border: 1px solid #999 !important;
            box-shadow: none !important;
        }

        /* Remove blue border on focus */
        .mud-input:focus,
        .mud-input-control:focus-within .mud-input,
        .mud-select:focus-within .mud-input,
        .mud-text-field:focus-within .mud-input,
        .mud-input-outlined:focus-within,
        .mud-input-outlined:focus-within .mud-input,
        .uniform-input-field:focus-within .mud-input,
        .template-name-field:focus-within .mud-input,
        .visit-type-container:focus-within .mud-input {
            border: 1px solid #666 !important;
            box-shadow: none !important;
            outline: none !important;
        }

        /* Remove all input field container hover and focus effects */
        .mud-input-control:hover,
        .mud-input-control:hover .mud-input-control-input-container,
        .mud-select:hover,
        .mud-text-field:hover,
        .mud-input-control:focus-within,
        .mud-select:focus-within,
        .mud-text-field:focus-within,
        .mud-input-outlined:hover,
        .mud-input-outlined:focus-within,
        .uniform-input-field:hover,
        .uniform-input-field:focus-within,
        .template-name-field:hover,
        .template-name-field:focus-within,
        .visit-type-container:hover,
        .visit-type-container:focus-within {
            border: none !important;
            box-shadow: none !important;
        }

    /* Remove any MudBlazor default focus styles */
    .mud-input-control-input-container:focus-within {
        border: none !important;
        box-shadow: none !important;
    }

    /* FIXED: Header section cards - with delete button positioning */
    .header-section-card {
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 12px;
        padding: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        position: relative;
    }

    /* FIXED: Header title row - delete button in extreme right */
    .header-title-row {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        margin-bottom: 8px !important;
        min-height: 36px !important;
    }

        .header-title-row .mud-button {
            padding: 4px 8px !important;
            min-height: 36px !important;
            flex: 1 !important;
            justify-content: flex-start !important;
        }

        .header-title-row .mud-icon-button {
            margin-left: auto !important;
            flex-shrink: 0 !important;
        }

    /* FIXED: Section cards - delete button in extreme right */
    .section-card {
        background-color: rgb(249, 250, 252) !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 6px !important;
        margin-bottom: 8px !important;
        margin-top: 8px !important;
        transition: box-shadow 0.2s ease !important;
    }

        .section-card .mud-card-header {
            background-color: #f5f5f5 !important;
            border-bottom: 1px solid #e0e0e0 !important;
            padding: 6px 12px !important;
        }

            .section-card .mud-card-header .d-flex {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                width: 100% !important;
            }

            .section-card .mud-card-header .mud-text {
                flex: 1 !important;
                text-align: left !important;
            }

            .section-card .mud-card-header .mud-icon-button {
                margin-left: auto !important;
                flex-shrink: 0 !important;
            }

    /* FIXED: Headers container - reduced padding */
    .headers-container {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 12px;
        background-color: #fafafa;
        margin-bottom: 16px;
        margin-top: 8px !important;
    }

    /* FIXED: Uniform field rows - consistent alignment */
    .uniform-field-row {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        margin-bottom: 8px !important;
    }

    .uniform-field-label {
        flex: 0 0 120px !important;
        text-align: left !important;
        font-weight: 500 !important;
        color: #424242 !important;
        margin-right: 16px !important;
    }

    .uniform-input-field {
        flex: 1 !important;
        max-width: 300px !important;
    }

        .uniform-input-field .mud-input-control {
            height: 36px !important;
            min-height: 36px !important;
        }

        .uniform-input-field .mud-input {
            height: 36px !important;
            padding: 0 12px !important;
            font-size: 14px !important;
            border: 1px solid #c4c4c4 !important;
            border-radius: 4px !important;
            background: white !important;
            box-shadow: none !important;
            outline: none !important;
        }

            /* Remove ALL effects on uniform input fields */
            .uniform-input-field .mud-input:hover,
            .uniform-input-field .mud-input-control:hover .mud-input {
                border: 1px solid #999 !important;
                box-shadow: none !important;
                outline: none !important;
            }

            .uniform-input-field .mud-input:focus,
            .uniform-input-field .mud-input-control:focus-within .mud-input {
                border: 1px solid #666 !important;
                box-shadow: none !important;
                outline: none !important;
            }

        /* Uniform input field labels - keep simple */
        .uniform-input-field .mud-input-label {
            position: static !important;
            top: auto !important;
            left: auto !important;
            transform: none !important;
            transition: none !important;
            font-size: 14px !important;
            color: #424242 !important;
            background: transparent !important;
            padding: 0 !important;
        }

    /* FIXED: Button group alignment */
    .uniform-button-group {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-end !important;
        margin-left: auto !important;
        flex: 1 !important;
    }

    .compact-button-group {
        gap: 2px !important;
    }

    .compact-style-button {
        font-size: 0.75rem !important;
        padding: 4px 8px !important;
        min-width: 60px !important;
        height: 32px !important;
        text-transform: none !important;
    }

    /* FIXED: Add Section button alignment */
    .add-section-button-row {
        display: flex !important;
        justify-content: flex-end !important;
        margin-top: 8px !important;
        margin-bottom: 8px !important;
    }

        .add-section-button-row .mud-button {
            height: 32px !important;
            padding: 4px 12px !important;
            font-size: 0.875rem !important;
        }

    /* FIXED: Professional button styling */
    .mud-button.mud-button-filled.mud-button-color-primary {
        background: #1976d2 !important;
        box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3) !important;
        transition: all 0.2s ease !important;
    }

        .mud-button.mud-button-filled.mud-button-color-primary:hover {
            background: #1565c0 !important;
            box-shadow: 0 3px 6px rgba(25, 118, 210, 0.4) !important;
        }

    /* FIXED: Icon button consistency */
    .mud-icon-button {
        height: 32px !important;
        width: 32px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: 32px !important;
    }

    /* FIXED: Submit button container */
    .submit-button-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 20px;
        padding-top: 16px;
        border-top: 1px solid #e0e0e0;
    }

    /* FIXED: Responsive adjustments */
    @@media (max-width: 768px) {
        .template-name-visit-type-row

    {
        flex-direction: column;
        gap: 8px;
    }

    .template-name-field,
    .visit-type-container {
        flex: 1 1 100%;
        max-width: 100%;
    }

    .uniform-field-label {
        flex: 0 0 100px !important;
        font-size: 0.875rem !important;
    }

    .uniform-input-field {
        max-width: 100% !important;
    }

    }
</style>